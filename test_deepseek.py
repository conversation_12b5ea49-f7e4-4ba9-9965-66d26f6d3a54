# test_deepseek.py - 快速测试DeepSeek配置

import os
from dotenv import load_dotenv

load_dotenv()

print("=== 检查DeepSeek配置 ===")
deepseek_key = os.environ.get("DEEPSEEK_API_KEY")
print(f"DEEPSEEK_API_KEY: {'SET' if deepseek_key else 'NOT SET'}")

if deepseek_key == "YOUR_DEEPSEEK_API_KEY_HERE":
    print("⚠️  请在.env文件中设置真实的DEEPSEEK_API_KEY")
else:
    print("✓ DeepSeek API密钥已配置")

print("\n=== 测试导入和初始化 ===")
try:
    from crewai import LLM
    llm = LLM(
        model="deepseek/deepseek-chat",
        temperature=0.5,
        api_key=deepseek_key
    )
    print("✓ DeepSeek LLM 初始化成功")
except Exception as e:
    print(f"✗ DeepSeek LLM 初始化失败: {e}")
