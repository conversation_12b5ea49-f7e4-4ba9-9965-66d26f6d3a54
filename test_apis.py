# test_apis.py - 测试各个API组件

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

print("=== 测试环境变量 ===")
gemini_key = os.environ.get("GEMINI_API_KEY")
firecrawl_key = os.environ.get("FIRECRAWL_API_KEY")
print(f"GEMINI_API_KEY: {'SET' if gemini_key else 'NOT SET'}")
print(f"FIRECRAWL_API_KEY: {'SET' if firecrawl_key else 'NOT SET'}")

print("\n=== 测试 Gemini API ===")
try:
    from crewai import LLM
    llm = LLM(
        model="gemini/gemini-1.5-flash",
        temperature=0.5,
        api_key=gemini_key
    )
    print("✓ Gemini LLM 初始化成功")
    
    # 简单测试 - 修正API调用方法
    try:
        # 尝试不同的调用方法
        response = llm.call("你好，请回复'测试成功'")
        print(f"✓ Gemini API 响应: {response}")
    except Exception as e2:
        print(f"✗ llm.call() 方法错误: {e2}")
        # 尝试其他方法
        try:
            from litellm import completion
            response = completion(
                model="gemini/gemini-1.5-flash",
                messages=[{"role": "user", "content": "你好，请回复'测试成功'"}],
                api_key=gemini_key
            )
            print(f"✓ 直接调用 Gemini API 成功: {response.choices[0].message.content}")
        except Exception as e3:
            print(f"✗ 直接调用也失败: {e3}")
    
except Exception as e:
    print(f"✗ Gemini API 错误: {e}")

print("\n=== 测试 Firecrawl API ===")
try:
    from firecrawl import FirecrawlApp
    app = FirecrawlApp(api_key=firecrawl_key)
    
    # 简单搜索测试 - 修正API调用方法
    try:
        search_results = app.search("杭州 江西菜 餐厅")
        print(f"✓ Firecrawl API 响应类型: {type(search_results)}")
    except Exception as e2:
        print(f"✗ 第一种调用方法失败: {e2}")
        try:
            # 尝试带参数的调用
            search_results = app.search(query="杭州 江西菜 餐厅", params={"limit": 2})
            print(f"✓ Firecrawl API (带参数) 响应类型: {type(search_results)}")
        except Exception as e3:
            print(f"✗ 第二种调用方法也失败: {e3}")
            return
    
    if hasattr(search_results, 'data'):
        print(f"✓ 搜索结果数量: {len(search_results.data)}")
        if search_results.data:
            first_result = search_results.data[0]
            print(f"✓ 第一个结果标题: {first_result.get('title', 'N/A')}")
    else:
        print(f"✓ 搜索结果: {search_results}")
        
except Exception as e:
    print(f"✗ Firecrawl API 错误: {e}")

print("\n=== 测试自定义 Firecrawl 工具 ===")
try:
    from src.tools.firecrawl_tool import FirecrawlSearchTool
    tool = FirecrawlSearchTool()
    result = tool._run("杭州 江西菜 餐厅")
    print(f"✓ 自定义工具响应长度: {len(result)} 字符")
    print(f"✓ 响应预览: {result[:200]}...")
    
except Exception as e:
    print(f"✗ 自定义工具错误: {e}")
