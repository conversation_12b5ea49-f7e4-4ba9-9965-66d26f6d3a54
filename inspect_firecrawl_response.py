import os
from dotenv import load_dotenv
from firecrawl import FirecrawlApp

# Load environment variables
load_dotenv()

# Initialize the Firecrawl app
app = FirecrawlApp(api_key=os.environ["FIRECRAWL_API_KEY"])

# Test the search API and inspect the response
try:
    print("Testing Firecrawl API directly and inspecting response...")
    search_results = app.search('best Jiangxi restaurants in Hangzhou')
    print("Type of search_results:", type(search_results))
    print("Attributes of search_results:", dir(search_results))
    print("search_results:", search_results)
    
    # Try to access the data
    if hasattr(search_results, 'data'):
        print("Data attribute:", search_results.data)
        print("Type of data:", type(search_results.data))
    else:
        print("No data attribute found")
        
except Exception as e:
    print(f"Error: {e}")
