# main.py

import os
from dotenv import load_dotenv
from crewai import Crew, Process, LLM

# **改变**: 导入我们自己创建的模块
from src.agents import create_agents
from src.tasks import create_tasks

# --- 1. 加载环境变量 ---
load_dotenv()

# --- 2. 配置 LLM ---
# **改变**: 使用 DeepSeek 模型替代 Gemini
llm = LLM(
    model="deepseek/deepseek-chat",
    temperature=0.5,
    api_key=os.environ.get("DEEPSEEK_API_KEY")
)

# --- 3. 创建 Agents 和 Tasks ---
critic_agent, planner_agent = create_agents(llm)
find_restaurants_task, plan_itinerary_task = create_tasks(critic_agent, planner_agent)

# --- 4. 组建并启动 Crew ---
food_crew = Crew(
  agents=[critic_agent, planner_agent],
  tasks=[find_restaurants_task, plan_itinerary_task],
  process=Process.sequential,
  verbose=True,
  # llm=llm # 你也可以在 Crew 层面统一设置 LLM
)

# 定义要执行任务的输入变量
inputs = {'cuisine': '江西菜'}

# 启动 Crew！
result = food_crew.kickoff(inputs=inputs)

# --- 5. 打印最终结果 ---
print("\n\n########################")
print("## 探店计划最终报告")
print("########################\n")
print(result)
