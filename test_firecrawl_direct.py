import os
from dotenv import load_dotenv
from firecrawl import FirecrawlApp

# Load environment variables
load_dotenv()

# Initialize the Firecrawl app
app = FirecrawlApp(api_key=os.environ["FIRECRAWL_API_KEY"])

# Test the search API
try:
    print("Testing Firecrawl API directly...")
    search_results = app.search('best Jiangxi restaurants in Hangzhou')
    print("Success! Here's the result:")
    print(search_results)
except Exception as e:
    print(f"Error: {e}")
