import os
from crewai.tools import BaseTool
from firecrawl import FirecrawlApp

class FirecrawlSearchTool(BaseTool):
    name: str = "Firecrawl Search"
    description: str = "使用 Firecrawl 进行网络搜索，返回搜索结果和内容摘要。"

    def _run(self, query: str) -> str:
        """使用工具。"""
        app = FirecrawlApp(api_key=os.environ["FIRECRAWL_API_KEY"])
        try:
            # Firecrawl 的 search API 返回一个 SearchResponse 对象
            # 该对象有一个 data 属性，包含搜索结果列表
            search_results = app.search(query)
            
            # 我们可以简化输出，只返回最重要的信息
            simplified_results = []
            # 获取搜索结果数据
            data = search_results.data if hasattr(search_results, 'data') else []
            for result in data[:5]:  # 只取前5个结果
                simplified_results.append({
                    'title': result.get('title'),
                    'url': result.get('url'),
                    'description': result.get('description')
                })
            
            # 将结果转换为字符串格式
            result_str = ""
            for i, res in enumerate(simplified_results, 1):
                result_str += f"{i}. {res['title']}\n   URL: {res['url']}\n   Description: {res['description']}\n\n"
            
            return result_str
        except Exception as e:
            return f"调用 Firecrawl API 时出错: {e}"
