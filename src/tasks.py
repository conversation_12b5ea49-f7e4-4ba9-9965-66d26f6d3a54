# src/tasks.py

from crewai import Task

def create_tasks(critic_agent, planner_agent):
    # 任务1: 寻找餐厅
    find_restaurants_task = Task(
      description=(
          "搜索在杭州的{cuisine}菜系中，评价最高的3家餐厅。"
          "专注于寻找那些有大量正面在线评论的餐厅，并综合考量搜索结果中的内容摘要。"
          "你的最终输出必须是一个包含餐厅名称、简短理由和地址的列表。"
      ),
      expected_output='一个包含3家餐厅信息的列表，格式如下：\n1. [餐厅名称] - [一句话推荐理由] - [地址]\n2. ...',
      agent=critic_agent # 分配给美食评论家
    )

    # 任务2: 规划行程
    plan_itinerary_task = Task(
      description=(
          "利用美食评论家找到的餐厅列表，为用户创建一个逻辑清晰的半日探店行程。"
          "行程应简单易懂，并建议一个合理的探店顺序。"
          "确保最终输出是面向用户的、友好的中文报告。"
      ),
      expected_output='一份格式化的半日行程计划，清晰地列出探店顺序和每家餐厅的信息。',
      agent=planner_agent, # 分配给行程规划师
      context=[find_restaurants_task] # **最佳实践**: 明确指出此任务依赖于上一个任务的输出
    )
    
    return find_restaurants_task, plan_itinerary_task