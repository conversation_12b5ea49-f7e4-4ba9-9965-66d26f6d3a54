# src/agents.py

from crewai import Agent
from src.tools.firecrawl_tool import FirecrawlSearchTool

# 传入在 main.py 中创建的 llm 实例
def create_agents(llm):
    # 初始化我们的自定义 Firecrawl 工具
    firecrawl_tool = FirecrawlSearchTool()

    # 1. 美食评论家
    critic = Agent(
      role='美食评论家',
      goal='根据指定的菜系和地点，从网上搜索并找到3家评价最高的餐厅。',
      backstory=(
          "你是一位经验丰富的美食评论家，以挑剔的味蕾和发现隐藏宝藏的能力而闻名。"
          "你擅长通过网络搜索，快速识别出那些在口味、服务和氛围方面都获得高度赞誉的餐厅。"
      ),
      verbose=True,
      allow_delegation=False,
      tools=[firecrawl_tool], # 使用我们自定义的 Firecrawl 工具
      llm=llm # 指定使用 Gemini 模型
    )

    # 2. 行程规划师
    planner = Agent(
      role='城市行程规划师',
      goal='根据给定的餐厅列表，为用户创建一个简单、合理的半日探店行程。',
      backstory=(
          "你是一位专业的行程规划师，擅长将零散的地点串联成一条有趣且高效的路线。"
          "你考虑地理位置和可能的交通方式，为用户提供清晰的行动建议。"
      ),
      verbose=True,
      allow_delegation=True,
      llm=llm # 指定使用 Gemini 模型
    )
    
    return critic, planner
