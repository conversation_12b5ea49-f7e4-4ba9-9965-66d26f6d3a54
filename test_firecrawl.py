import os
from dotenv import load_dotenv
from crewai_tools import FirecrawlSearchTool

# Load environment variables
load_dotenv()

# Initialize the Firecrawl tool
firecrawl_tool = FirecrawlSearchTool()

# Test the tool
try:
    print("Testing Firecrawl API...")
    result = firecrawl_tool._run("best Jiangxi restaurants in Hangzhou")
    print("Success! Here's the result:")
    print(result)
except Exception as e:
    print(f"Error: {e}")
